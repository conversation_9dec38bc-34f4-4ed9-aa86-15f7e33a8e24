package routers

import (
	"time"

	"api/config"
	"api/http/admin_controllers"
	"api/http/controllers"
	"api/http/routers/middlewares"

	"github.com/gin-gonic/gin"
)

// 发短信的限流器
var smsThrottleMiddleware gin.HandlerFunc

// 初始化router需要的组件
func Init() error {
	var err error
	smsThrottleMiddleware, err = middlewares.ThrottleInstance("APP_SMS_THROTTLE", time.Minute, 1)
	if err != nil {
		return err
	}
	return err
}

func Register(g *gin.Engine, mw ...gin.HandlerFunc) *gin.Engine {

	g.Use(gin.Recovery())
	g.Use(mw...)
	g.NoRoute(func(c *gin.Context) {
		c.AbortWithStatus(404)
	})
	g.HandleMethodNotAllowed = true
	g.ForwardedByClientIP = true

	// var authorizeUserWithRBAC = []gin.HandlerFunc{middlewares.Authorize, middlewares.CasbinRBAC, middlewares.UserRole}
	var authorizeUser = []gin.HandlerFunc{middlewares.Authorize}
	v1 := g.Group("/v1", func(c *gin.Context) {
		c.Request.Header.Set(config.APIRole, config.TokenRoleUser)
	})
	{

		// v1.Use(middlewares.SignatureCheck)
		// 图形校验
		imageCaptchaGroup := v1.Group("image-captcha")
		{
			imageCaptchaGroup.GET("generate", controllers.ImageCaptchaGenerate)
		}

		api := v1.Group("/tags")
		{
			// 标签相关路由
			// 获取所有标签分类
			api.GET("/categories", controllers.GetAllCategories)
			// 获取指定分类下的标签
			api.GET("/by-category/:categoryId", controllers.GetTagsByCategory)
		}

		// 艺术品数字资产库
		DigitalAssetGroup := v1.Group("digital-asset")
		{
			// banner
			DigitalAssetGroup.GET("banner", controllers.DigitalBanner)
			// 数字资产专题1列表
			DigitalAssetGroup.GET("subject1", controllers.DigitalSubject1)
			// 数字资产专题2列表
			DigitalAssetGroup.GET("subject2", controllers.DigitalSubject2)
			// 艺术品详情
			DigitalAssetGroup.GET("detail/:copyright_id", controllers.DigitalAssetDetail)
			// 艺术品检索
			DigitalAssetGroup.GET("search", controllers.DigitalAssetSearch)
			// 访问量
			DigitalAssetGroup.POST("view", controllers.DigitalAssetView)
			// 热门推荐
			DigitalAssetGroup.GET("recommend", controllers.DigitalAssetRecommend)
		}
		publicGroup := v1.Group("/public")
		{
			// 创建反馈
			publicGroup.POST("/feedbacks", controllers.CreateFeedback)
			// 获取上传令牌
			publicGroup.GET("/feedback/token", controllers.GenerateTempToken)

			// 文件上传路由
			publicGroup.POST("/upload/file", controllers.PublicUploadFile)
		}

		wxAuth := v1.Group("/wx")
		{
			wxAuth.POST("/login", controllers.WxLogin) // 微信登录
		}

		authGroup := v1.Group("auth")
		{
			authGroup.POST("login", controllers.Login)
			authGroup.POST("forget-pwd", middlewares.SmsVerify, controllers.UserForgetPwd)
			authGroup.POST("register", middlewares.SmsVerify, controllers.UserRegister)
			// authGroup.POST("register", controllers.UserRegister)
			authGroup.POST("artist-register", middlewares.SmsVerify, controllers.ArtistRegister)
			authGroup.POST("agency-register", middlewares.SmsVerify, controllers.AgencyRegister)
			authGroup.POST("sms", middlewares.ImageCaptchaVerify, smsThrottleMiddleware, controllers.SendSMS)
			// authGroup.POST("sms", controllers.SendSMS)
			authGroup.Use(middlewares.Authorize)
			{
				authGroup.GET("index", controllers.UserIndex)
				authGroup.POST("realname", controllers.Realname)
				authGroup.GET("user-info", controllers.UserInfo)
				authGroup.POST("user-info-update", controllers.UserInfoUpdate)
				authGroup.POST("update-telephone", middlewares.SmsVerify, controllers.UserUpdateTelephone)
				// authGroup.POST("update-telephone", controllers.UserUpdateTelephone)
				authGroup.POST("agency-info-update", controllers.AgencyInfoUpdate)
				authGroup.POST("reset-pwd", middlewares.SmsVerify, controllers.UserResetPwd)
				authGroup.POST("logout", controllers.LogOut)
				// 金融级实名认证相关路由
				//authGroup.POST("finance-realname/init", controllers.InitFinanceRealname)
				//authGroup.POST("finance-realname/verify", controllers.VerifyFinanceRealname)
			}
		}

		//公共路由
		front := v1.Group("/front")
		{
			front.GET("/display-partners", controllers.GetHomeDisplayPartners)
			front.GET("/partner", controllers.GetHomePartners)         // 合作伙伴列表
			front.GET("/carousels", controllers.GetHomeCarousels)      //轮播图
			front.GET("/home", controllers.HomeIndex)                  // 首页数据
			front.GET("/news/:id", controllers.NewsDetail)             // 资讯详情
			front.GET("/exhibition/:id", controllers.ExhibitionDetail) // 展览详情
		}
		v1.Use(authorizeUser...)
		authorGroup := v1.Group("author")
		{
			// 创建作者
			authorGroup.POST("create", controllers.AuthorCreate)
			// 更新作者
			authorGroup.POST("update", controllers.AuthorUpdate)
			// 删除作者
			authorGroup.POST("delete", controllers.AuthorDelete)
			// 作者列表
			authorGroup.GET("list", controllers.AuthorList)
		}

		// 作者认证
		authorAuthGroup := v1.Group("author-auth")
		{
			// 申请作者认证
			authorAuthGroup.POST("apply", controllers.AuthorAuthApply)
			// 作者认证通过
			authorAuthGroup.POST("pass", controllers.AuthorPass)
			// 作者认证驳回
			authorAuthGroup.POST("reject", controllers.AuthorReject)
			// 作者认证列表
			authorAuthGroup.GET("list", controllers.AuthorAuthList)
			// 作者认证详情
			authorAuthGroup.GET("detail/:no", controllers.AuthorAuthDetail)
		}

		// 版权
		copyrightGroup := v1.Group("copyright")
		{
			// 版权存证
			copyrightGroup.POST("store", controllers.CopyrightStore)
			// 版权存证列表
			copyrightGroup.GET("list", controllers.CopyrightList)
			// 版权存证详情
			copyrightGroup.GET("detail/:no", controllers.CopyrightDetail)
			// 下载证书文件
			copyrightGroup.GET("detail/:no/certificate-file", controllers.CertificateFile)
			// 下载上传的文件
			copyrightGroup.GET("detail/:no/file/:id", controllers.PrivateFile)
		}
		//机构类别管理
		institutionCategory := v1.Group("/institution-category")
		{
			institutionCategory.GET("", controllers.CategoryList)
		}

		// 取证
		infringementGroup := v1.Group("infringement")
		{
			// 取证申请
			infringementGroup.POST("store", controllers.InfringementStore)
			// 取证列表
			infringementGroup.GET("list", controllers.InfringementList)
			// 取证详情
			infringementGroup.GET("detail/:no", controllers.InfringementDetail)
		}

		// 上传
		uploadGroup := v1.Group("upload")
		{
			uploadGroup.GET("sign", controllers.UploadSign)
			uploadGroup.POST("file", controllers.UploadFile)
		}

		// 获取文件下载链接
		v1.GET("private-file", controllers.PrivateFileByPath)

		// 消息管理
		messageGroup := v1.Group("message")
		{
			// 获取用户消息列表
			messageGroup.GET("list", controllers.MessageList)
			// 标记消息已读
			messageGroup.POST("read", controllers.MessageRead)

			// 保获取微信模板配置
			messageGroup.GET("/wechat/templates", controllers.GetWechatTemplates)

			// 保存用户订阅状态
			messageGroup.POST("/subscribe", controllers.SaveSubscribeStatus)
		}

	}

	return g
}

func RegisterAdmin(g *gin.Engine, mw ...gin.HandlerFunc) *gin.Engine {

	g.Use(gin.Recovery())
	g.Use(mw...)
	g.NoRoute(func(c *gin.Context) {
		c.AbortWithStatus(404)
	})
	g.HandleMethodNotAllowed = true
	g.ForwardedByClientIP = true

	v1 := g.Group("/v1", func(c *gin.Context) {
		c.Request.Header.Set(config.APIRole, config.TokenRoleAdmin)
	})
	{
		// 取证回调接口
		callbackGroup := v1.Group("callback")
		{
			callbackGroup.POST("infringement", admin_controllers.CallbackInfringement)
		}

		authGroup := v1.Group("auth")
		{
			// authGroup.POST("login", admin_controllers.Login)
			authGroup.POST("login", admin_controllers.Login)

			authGroup.Use(middlewares.AdminAuthorize)
			{
				authGroup.GET("user-info", admin_controllers.AdminUserInfo)
				authGroup.POST("logout", admin_controllers.LogOut)
			}
		}
		var adminAuthorize = []gin.HandlerFunc{middlewares.AdminAuthorize}
		v1.Use(adminAuthorize...)
		//管理员角色控制
		admin := v1.Group("/admin")
		{
			{
				admin.GET("", admin_controllers.AdminList)                         // 管理员列表
				admin.GET("/:id", admin_controllers.AdminDetail)                   // 管理员详情
				admin.POST("", admin_controllers.AdminCreate)                      // 创建管理员
				admin.POST("/:id", admin_controllers.AdminUpdate)                  // 更新管理员
				admin.POST("/:id/delete", admin_controllers.AdminDelete)           // 删除管理员
				admin.POST("/:id/password", admin_controllers.AdminUpdatePassword) // 重置密码
			}
		}
		role := v1.Group("/roles")
		{
			role.GET("", admin_controllers.RoleList)                            // 角色列表
			role.POST("/:id/toggle-status", admin_controllers.RoleToggleStatus) // 切换角色状态
		}
		//咨询管理
		news := v1.Group("/news")
		{
			news.GET("", admin_controllers.NewsList)                      // 资讯列表
			news.POST("", admin_controllers.NewsCreate)                   // 创建资讯
			news.POST("/:id", admin_controllers.NewsUpdate)               // 更新资讯
			news.POST("/:id/delete", admin_controllers.NewsDelete)        // 删除资讯
			news.POST("/:id/publish", admin_controllers.NewsPublish)      // 发布资讯
			news.POST("/:id/cancel", admin_controllers.NewsCancelPublish) // 取消发布资讯
		}
		// 展览管理路由
		exhibitions := v1.Group("/exhibitions")
		{
			exhibitions.GET("", admin_controllers.ExhibitionList)                      // 展览列表
			exhibitions.POST("", admin_controllers.ExhibitionCreate)                   // 创建展览
			exhibitions.POST("/:id", admin_controllers.ExhibitionUpdate)               // 更新展览
			exhibitions.POST("/:id/delete", admin_controllers.ExhibitionDelete)        // 删除展览
			exhibitions.POST("/:id/publish", admin_controllers.ExhibitionPublish)      // 发布展览
			exhibitions.POST("/:id/cancel", admin_controllers.ExhibitionCancelPublish) // 取消发布展览
		}
		//机构类别管理
		institutionCategory := v1.Group("/institution-category")
		{
			institutionCategory.GET("", admin_controllers.CategoryList)
			institutionCategory.POST("", admin_controllers.CategoryCreate)
			institutionCategory.POST("/:id", admin_controllers.CategoryUpdate)
			institutionCategory.POST("/:id/update-status", admin_controllers.CategoryUpdateStatus)
		}
		// 标签业务路由组
		tagBusiness := v1.Group("/tags")
		{
			// 标签分类
			tagCategoryBusiness := tagBusiness.Group("/categories")
			{
				tagCategoryBusiness.GET("", admin_controllers.TagCategoryList)                        // 获取分类列表
				tagCategoryBusiness.POST("", admin_controllers.TagCategoryCreate)                     // 创建分类
				tagCategoryBusiness.POST("/update/:id", admin_controllers.TagCategoryUpdate)          // 更新分类
				tagCategoryBusiness.POST("/batch-enable", admin_controllers.TagCategoryBatchEnable)   // 批量启用分类
				tagCategoryBusiness.POST("/batch-disable", admin_controllers.TagCategoryBatchDisable) // 批量禁用分类
			}

			// 标签
			tagBusiness.GET("", admin_controllers.TagList)                         // 获取标签列表
			tagBusiness.POST("", admin_controllers.TagCreate)                      // 创建标签
			tagBusiness.POST("/update/:id", admin_controllers.TagUpdate)           // 更新标签
			tagBusiness.POST("/batch-enable", admin_controllers.TagsBatchEnable)   // 批量启用标签
			tagBusiness.POST("/batch-disable", admin_controllers.TagsBatchDisable) // 批量禁用标签

			// 标签业务关联
			//tagBusiness.GET("/business", admin_controllers.GetBusinessTags)               // 获取业务标签列表
			//tagBusiness.POST("/business", admin_controllers.TagBusinessCreate)            // 创建标签业务关联
			//tagBusiness.POST("/business/:id/delete", admin_controllers.TagBusinessDelete) // 删除标签业务关联
		}

		// 存证
		copyrightGroup := v1.Group("copyright")
		{
			// 存证列表
			copyrightGroup.GET("list", admin_controllers.CopyrightList)
			// 存证详情
			copyrightGroup.GET("detail/:no", admin_controllers.CopyrightDetail)
		}

		// 取证
		infringementGroup := v1.Group("infringement")
		{
			// 取证列表
			infringementGroup.GET("list", admin_controllers.InfringementList)
			// 取证详情
			infringementGroup.GET("detail/:no", admin_controllers.InfringementDetail)
		}

		// 数字资产
		digitalAssetsGroup := v1.Group("digital-assets")
		{
			// 资产概览
			digitalAssetsGroup.GET("index", admin_controllers.DigitalAssetsIndex)
			// 数字资产管理列表
			digitalAssetsGroup.GET("list", admin_controllers.DigitalAssetsList)
			// 入库审核列表
			digitalAssetsGroup.GET("store-list", admin_controllers.StoreList)
			// 数字资产详情
			digitalAssetsGroup.GET("detail/:no", admin_controllers.DigitalAssetsDetail)
			// 入库
			digitalAssetsGroup.POST("store", admin_controllers.DigitalAssetsStore)
			// 拒绝入库
			digitalAssetsGroup.POST("reject", admin_controllers.DigitalAssetsReject)
			// 上架
			digitalAssetsGroup.POST("shelve", admin_controllers.DigitalAssetsShelve)
			// 下架
			digitalAssetsGroup.POST("off-shelve", admin_controllers.DigitalAssetsOffShelve)
			// 删除
			digitalAssetsGroup.POST("delete", admin_controllers.DigitalAssetsDelete)
			// 修改标签
			digitalAssetsGroup.POST("update-labels", admin_controllers.DigitalUpdateLabels)
		}

		// 宋庄认证管理
		SzAuthGroup := v1.Group("sz-auth")
		{
			// 认证类型下拉
			SzAuthGroup.GET("types", admin_controllers.SzAuthTypes)
			// 列表
			SzAuthGroup.GET("list", admin_controllers.SzAuthList)
			// 导出
			SzAuthGroup.GET("list-export", admin_controllers.ListExportExcel)
			// 导入
			SzAuthGroup.POST("list-import", admin_controllers.ListImport)
			// 下载模版
			SzAuthGroup.GET("template", admin_controllers.SzAuthTemplate)
		}

		// 专题管理
		subjectGroup := v1.Group("subject")
		{
			// 专题类型下拉菜单
			subjectGroup.GET("position", admin_controllers.SubjectPosition)
			// 专题创建
			subjectGroup.POST("create", admin_controllers.SubjectCreate)
			// 专题列表
			subjectGroup.GET("list", admin_controllers.SubjectList)
			// 更新发布状态
			subjectGroup.POST("update-status", admin_controllers.SubjectUpdateStatus)
			// 删除
			subjectGroup.POST("delete", admin_controllers.SubjectDelete)
			// 专题绑定内容列表
			subjectGroup.GET("bind-list", admin_controllers.SubjectBindList)
			// 获取某专题的可新增资源列表
			subjectGroup.GET("insert-list", admin_controllers.SubjectInsertList)
			// 专题更新
			subjectGroup.POST("update", admin_controllers.SubjectUpdate)
			// 专题内容绑定
			subjectGroup.POST("bind-relation", admin_controllers.SubjectBindRelation)
			// 专题内容删除
			subjectGroup.POST("bind-relation-delete", admin_controllers.SubjectBindRelationDelete)
		}

		//轮播图管理
		carousel := v1.Group("/carousel")
		{
			carousel.GET("", admin_controllers.CarouselList)               // 轮播图列表
			carousel.POST("", admin_controllers.CarouselCreate)            // 创建轮播图
			carousel.POST("/batch", admin_controllers.CarouselBatchStatus) // 批量更新状态
			carousel.POST("/delete/:id", admin_controllers.CarouselDelete) // 删除轮播图
		}
		agency := v1.Group("/agency")
		{
			agency.GET("", admin_controllers.AgencyList)             // 机构列表
			agency.GET("/:id", admin_controllers.AgencyDetail)       // 机构详情
			agency.POST("/:id/audit", admin_controllers.AgencyAudit) // 机构审核
		}
		users := v1.Group("/users")
		{
			// 用户管理
			users.GET("/normal", admin_controllers.GetNormalUsers)   // 普通用户列表
			users.GET("/artists", admin_controllers.GetArtistUsers)  // 艺术家列表
			users.GET("/agencies", admin_controllers.GetAgencyUsers) // 机构用户列表
			users.POST("/:id/lock", admin_controllers.LockUser)      // 锁定/解锁用户
		}
		// 用户反馈 路由组
		feedbackGroup := v1.Group("/feedbacks")
		{
			feedbackGroup.GET("", admin_controllers.ListFeedbacks)
			feedbackGroup.GET("/:id", admin_controllers.GetFeedbackDetail)
			feedbackGroup.POST("/:id/process", admin_controllers.ProcessFeedback)
		}
		// 用户上传
		uploadGroup := v1.Group("upload")
		{
			uploadGroup.GET("sign", admin_controllers.UploadSign)
			uploadGroup.POST("file", admin_controllers.UploadFile)
		}

	}
	// 获取文件下载链接
	v1.GET("private-file", admin_controllers.PrivateFileByPath)

	partner := v1.Group("/partner")
	{
		partner.GET("", admin_controllers.PartnerList)                         // 合作伙伴列表
		partner.POST("/create", admin_controllers.PartnerCreate)               // 创建合作伙伴
		partner.POST("/update/:id", admin_controllers.PartnerUpdate)           // 更新合作伙伴
		partner.POST("/status/publish", admin_controllers.PartnerPublish)      // 发布合作伙伴（支持批量）
		partner.POST("/status/unpublish", admin_controllers.PartnerUnpublish)  // 取消发布（支持批量）
		partner.POST("/delete/:id", admin_controllers.PartnerDelete)           // 删除合作伙伴
		partner.POST("/batch-set-home", admin_controllers.PartnerBatchSetHome) // 批量设置合作伙伴为首页展示
	}
	// 数据概览相关路由
	dashboard := v1.Group("/dashboard")
	{
		dashboard.GET("/overview", admin_controllers.GetDashboardOverview) // 数据概览
		dashboard.GET("/todo", admin_controllers.GetTodoList)              // 待办事项
	}
	return g
}
