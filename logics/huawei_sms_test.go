package logics

import (
	"context"
	"testing"
	"time"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestHuaweiSMSConfig(t *testing.T) {
	// 设置测试配置
	viper.Set("huawei_sms_api_address", "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1")
	viper.Set("huawei_sms_app_key", "test_app_key")
	viper.Set("huawei_sms_app_secret", "test_app_secret")
	viper.Set("huawei_sms_sender", "test_sender")
	viper.Set("huawei_sms_template_id", "test_template_id")
	viper.Set("huawei_sms_signature", "test_signature")
	viper.Set("huawei_sms_concurrent_limit", 5)

	client := getHuaweiSMSClient()
	
	// 测试配置验证
	err := client.validateConfig()
	assert.NoError(t, err)
	
	// 测试请求体构建
	body := client.buildRequestBody("+8613800138000", "123456")
	assert.Contains(t, body, "from=test_sender")
	assert.Contains(t, body, "to=%2B8613800138000")
	assert.Contains(t, body, "templateId=test_template_id")
	assert.Contains(t, body, "templateParas=%5B%22123456%22%5D")
	assert.Contains(t, body, "signature=test_signature")
	
	// 测试请求头构建
	headers := client.buildHeaders()
	assert.Equal(t, "application/x-www-form-urlencoded", headers["Content-Type"])
	assert.Equal(t, AUTH_HEADER_VALUE, headers["Authorization"])
	assert.NotEmpty(t, headers["X-WSSE"])
}

func TestHuaweiSMSConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]string
		expectError bool
	}{
		{
			name: "valid config",
			config: map[string]string{
				"huawei_sms_api_address": "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1",
				"huawei_sms_app_key":     "test_key",
				"huawei_sms_app_secret":  "test_secret",
				"huawei_sms_sender":      "test_sender",
				"huawei_sms_template_id": "test_template",
			},
			expectError: false,
		},
		{
			name: "missing api address",
			config: map[string]string{
				"huawei_sms_app_key":     "test_key",
				"huawei_sms_app_secret":  "test_secret",
				"huawei_sms_sender":      "test_sender",
				"huawei_sms_template_id": "test_template",
			},
			expectError: true,
		},
		{
			name: "missing app key",
			config: map[string]string{
				"huawei_sms_api_address": "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1",
				"huawei_sms_app_secret":  "test_secret",
				"huawei_sms_sender":      "test_sender",
				"huawei_sms_template_id": "test_template",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 清除之前的配置
			viper.Reset()
			
			// 设置测试配置
			for key, value := range tt.config {
				viper.Set(key, value)
			}
			
			// 重置单例
			once = sync.Once{}
			huaweiSMSClient = nil
			
			client := getHuaweiSMSClient()
			err := client.validateConfig()
			
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestHuaweiSMSConcurrency(t *testing.T) {
	// 设置测试配置
	viper.Set("huawei_sms_concurrent_limit", 2)
	viper.Set("huawei_sms_api_address", "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1")
	viper.Set("huawei_sms_app_key", "test_app_key")
	viper.Set("huawei_sms_app_secret", "test_app_secret")
	viper.Set("huawei_sms_sender", "test_sender")
	viper.Set("huawei_sms_template_id", "test_template_id")
	
	// 重置单例
	once = sync.Once{}
	huaweiSMSClient = nil
	
	client := getHuaweiSMSClient()
	
	// 测试并发控制
	assert.Equal(t, 2, cap(client.semaphore))
	
	// 模拟并发请求
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()
	
	// 占用所有信号量
	client.semaphore <- struct{}{}
	client.semaphore <- struct{}{}
	
	// 这个请求应该被阻塞并超时
	start := time.Now()
	err := SendHuaweiSMS(ctx, "+8613800138000", "123456")
	duration := time.Since(start)
	
	// 应该因为超时而失败
	assert.Error(t, err)
	assert.True(t, duration >= 1*time.Second)
	
	// 释放信号量
	<-client.semaphore
	<-client.semaphore
}

func TestSendFunction(t *testing.T) {
	// 测试华为云短信未启用的情况
	viper.Set("use_huawei_sms", false)
	
	ctx := context.Background()
	err := Send(ctx, "+8613800138000", "123456")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "短信服务未启用")
	
	// 测试华为云短信启用但配置错误的情况
	viper.Set("use_huawei_sms", true)
	viper.Set("huawei_sms_api_address", "") // 空配置
	
	// 重置单例
	once = sync.Once{}
	huaweiSMSClient = nil
	
	err = Send(ctx, "+8613800138000", "123456")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "API地址未配置")
}
