package logics

import (
	"bytes"
	"context"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/satori/go.uuid"
	log "github.com/micro/go-micro/v2/logger"
	"github.com/spf13/viper"
)

// 华为云短信服务配置
type HuaweiSMSConfig struct {
	APIAddress   string
	AppKey       string
	AppSecret    string
	Sender       string
	TemplateID   string
	Signature    string
}

// 华为云短信客户端
type HuaweiSMSClient struct {
	config     *HuaweiSMSConfig
	httpClient *http.Client
	semaphore  chan struct{} // 用于控制并发数
	mutex      sync.RWMutex
}

// 短信发送请求
type SMSRequest struct {
	Telephone string
	Code      string
}

// 短信发送响应
type SMSResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Result  []struct {
		OriginTo    string `json:"originTo"`
		CreateTime  string `json:"createTime"`
		From        string `json:"from"`
		SmsMsgId    string `json:"smsMsgId"`
		CountryId   string `json:"countryId"`
		Status      string `json:"status"`
	} `json:"result"`
}

const (
	// 华为云短信认证相关常量
	WSSE_HEADER_FORMAT = "UsernameToken Username=\"%s\",PasswordDigest=\"%s\",Nonce=\"%s\",Created=\"%s\""
	AUTH_HEADER_VALUE  = "WSSE realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\""
	
	// 默认并发控制数量
	DEFAULT_CONCURRENT_LIMIT = 10
)

var (
	huaweiSMSClient *HuaweiSMSClient
	once            sync.Once
)

// 获取华为云短信客户端单例
func getHuaweiSMSClient() *HuaweiSMSClient {
	once.Do(func() {
		config := &HuaweiSMSConfig{
			APIAddress: viper.GetString("huawei_sms_api_address"),
			AppKey:     viper.GetString("huawei_sms_app_key"),
			AppSecret:  viper.GetString("huawei_sms_app_secret"),
			Sender:     viper.GetString("huawei_sms_sender"),
			TemplateID: viper.GetString("huawei_sms_template_id"),
			Signature:  viper.GetString("huawei_sms_signature"),
		}

		// 创建HTTP客户端，跳过SSL验证
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		httpClient := &http.Client{
			Transport: tr,
			Timeout:   30 * time.Second,
		}

		// 获取并发限制配置，默认为10
		concurrentLimit := viper.GetInt("huawei_sms_concurrent_limit")
		if concurrentLimit <= 0 {
			concurrentLimit = DEFAULT_CONCURRENT_LIMIT
		}

		huaweiSMSClient = &HuaweiSMSClient{
			config:     config,
			httpClient: httpClient,
			semaphore:  make(chan struct{}, concurrentLimit),
		}
	})
	return huaweiSMSClient
}

// 发送短信
func SendHuaweiSMS(ctx context.Context, telephone string, code string) error {
	client := getHuaweiSMSClient()
	
	// 验证配置
	if err := client.validateConfig(); err != nil {
		return fmt.Errorf("华为云短信配置错误: %w", err)
	}

	// 并发控制
	select {
	case client.semaphore <- struct{}{}:
		defer func() { <-client.semaphore }()
	case <-ctx.Done():
		return ctx.Err()
	}

	// 构建请求
	requestBody := client.buildRequestBody(telephone, code)
	headers := client.buildHeaders()

	// 发送请求
	response, err := client.sendRequest(ctx, requestBody, headers)
	if err != nil {
		log.Errorf("华为云短信发送失败: %v", err)
		return fmt.Errorf("短信发送失败: %w", err)
	}

	// 解析响应
	return client.parseResponse(response)
}

// 验证配置
func (c *HuaweiSMSClient) validateConfig() error {
	if c.config.APIAddress == "" {
		return fmt.Errorf("API地址未配置")
	}
	if c.config.AppKey == "" {
		return fmt.Errorf("AppKey未配置")
	}
	if c.config.AppSecret == "" {
		return fmt.Errorf("AppSecret未配置")
	}
	if c.config.Sender == "" {
		return fmt.Errorf("发送方未配置")
	}
	if c.config.TemplateID == "" {
		return fmt.Errorf("模板ID未配置")
	}
	return nil
}

// 构建请求体
func (c *HuaweiSMSClient) buildRequestBody(telephone, code string) string {
	// 模板变量，验证码短信
	templateParas := fmt.Sprintf("[\"%s\"]", code)
	
	param := "from=" + url.QueryEscape(c.config.Sender) +
		"&to=" + url.QueryEscape(telephone) +
		"&templateId=" + url.QueryEscape(c.config.TemplateID) +
		"&templateParas=" + url.QueryEscape(templateParas)
	
	if c.config.Signature != "" {
		param += "&signature=" + url.QueryEscape(c.config.Signature)
	}
	
	return param
}

// 构建请求头
func (c *HuaweiSMSClient) buildHeaders() map[string]string {
	headers := make(map[string]string)
	headers["Content-Type"] = "application/x-www-form-urlencoded"
	headers["Authorization"] = AUTH_HEADER_VALUE
	headers["X-WSSE"] = c.buildWSSEHeader()
	return headers
}

// 构建WSSE认证头
func (c *HuaweiSMSClient) buildWSSEHeader() string {
	cTime := time.Now().Format("2006-01-02T15:04:05Z")
	nonce := uuid.NewV4().String()
	nonce = strings.ReplaceAll(nonce, "-", "")

	h := sha256.New()
	h.Write([]byte(nonce + cTime + c.config.AppSecret))
	passwordDigestBase64Str := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return fmt.Sprintf(WSSE_HEADER_FORMAT, c.config.AppKey, passwordDigestBase64Str, nonce, cTime)
}

// 发送HTTP请求
func (c *HuaweiSMSClient) sendRequest(ctx context.Context, body string, headers map[string]string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "POST", c.config.APIAddress, bytes.NewBufferString(body))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// 解析响应
func (c *HuaweiSMSClient) parseResponse(responseBody []byte) error {
	var smsResp SMSResponse
	if err := json.Unmarshal(responseBody, &smsResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if smsResp.Code != "000000" {
		return fmt.Errorf("短信发送失败，错误码: %s, 错误信息: %s", smsResp.Code, smsResp.Message)
	}

	// 检查发送结果
	if len(smsResp.Result) > 0 {
		result := smsResp.Result[0]
		if result.Status != "000000" {
			return fmt.Errorf("短信发送失败，状态: %s", result.Status)
		}
		log.Infof("短信发送成功，消息ID: %s", result.SmsMsgId)
	}

	return nil
}
