package logics

import (
	"context"
	"errors"

	log "github.com/micro/go-micro/v2/logger"
	"github.com/spf13/viper"
)

// Send 发送短信 - 现在使用华为云短信服务
func Send(ctx context.Context, telephone string, code string) error {
	// 检查华为云短信配置是否启用
	useHuaweiSMS := viper.GetBool("use_huawei_sms")
	if !useHuaweiSMS {
		log.Error("华为云短信服务未启用")
		return errors.New("短信服务未启用")
	}

	// 使用华为云短信服务发送
	err := SendHuaweiSMS(ctx, telephone, code)
	if err != nil {
		log.Errorf("华为云短信发送失败: %v", err)
		return err
	}

	log.Infof("短信发送成功，手机号: %s", telephone)
	return nil
}
