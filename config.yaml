port: 9996
admin_port: 8846
timezone: Asia/Shanghai
auth_secret: 010accd216e3dda1267e6fe83b454372
encrypt_salt: eab7ef684d26befcb3fb1ad6ee2600a6
token_expired_second: 18000

#数据库
# db_host: 127.0.0.1
# db_port: 3306
# db_database: my_app_songzhuang
# db_username: root
# db_password: 123456
# db_table_prefix: cp_
db_host: *************
db_port: 30336
db_database: copyright
db_username: root
db_password: PasW00r!d
db_table_prefix: mg_

#db_host_resolver: 127.0.0.1
#db_port_resolver: 3306
#db_database_resolver: my_app_migu2
#db_username_resolver: root
#db_password_resolver: 123456


#redis
redis_host: *************
redis_port: 30086
redis_db: 0
redis_password: Zhi123Gui

#wechat
wechat_app_id: wxf7e50934c249ff3b
wechat_app_secret: 81f126af63d3f59ce71850c588675f1e

# micro
micro_storage_service: com.zhigui.xian.s3.aws
micro_blockchain_service: com.zhigui.xian.blockchain.fabric.sdk.restful
micro_make_certificate_sub: com.zhigui.xian.make-certificate
micro_copyright_platform_service: PLATFORM_SERVICE
micro_image_captcha_service: com.zhigui.xian.micro.service.image.captcha
micro_copyright_platform_certificate_path_sub: PLATFORM_CERTIFICATE_PATH_SUB
micro_copyright_platform_copyright_stored_event: PLATFORM_COPYRIGHT_STORED_EVENT
micro_copyright_platform_copyright_invoked_event: PLATFORM_COPYRIGHT_INVOKED_EVENT
micro_copyright_platform_infringement_stored_event: PLATFORM_INFRINGEMENT_STORED_EVENT
micro_copyright_platform_infringement_invoked_event: PLATFORM_INFRINGEMENT_INVOKED_EVENT

micro_copyright_platform_operation_log_event:

# copyright
# copyright_download_url: https://s3.develop.xian.zhigui.com/copyright/copyright/template/copyright-seal-hanyang.zip
# copyright_download_url: http://s3.develop.xian.zhigui.com/copyright/copyright/template/copyright-seal-migu.zip
copyright_download_url: http://*************:30095/copyright/copyright/template/copyright-seal-songzhuang.zip
infringement_download_url: http://*************:30095/copyright/copyright/template/copyright-seal-songzhuang.zip

# excel template
sz_auth_excel_template_url: http://*************:30095/copyright/sz-auth-template.xlsx

bid_create_api: https://api-dev.zhigui.com/bcs/xh/v1/sign/create
bid_type: 105
bid_template_name: test
bid_info_api: https://api-dev.zhigui.com/bcs/baas-api/v1/user/info
bid_username: lixuc001
bid_password: 123qwe
bid_login_api: https://api-dev.zhigui.com/bcs/baas-api/v1/user/login

default_institution_id: 1001
default_institution_name: "默认机构"

sync_label_batch_number: 500
ftp_addr: *************:30021
ftp_user: migu
ftp_pwd: 123456
ftp_path:

picture_domain: *************:30900

bif_cron_time: "0 */1 * * * *"
bif_cron_count: 20
bif_sync_url: "http://test-openapi.bitfactory.cn"
bif_sync_acsn: "hy02"
bif_api_key: "did:bid:efCF8TTWAnNCShBSS6PCj7sSDccuEHvv"
bif_api_secret: "cb620dd92ca4440cc76b555322f2a4e65f65a2d8"

infringement_service_api: "http://*************:32681/fetch-url"

aliyun_access_key: "your_access_key"
aliyun_access_secret: "your_access_secret"
aliyun_face_scene_id: "your_scene_id"


# 华为云短信服务配置
use_huawei_sms: true
huawei_sms_api_address: "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1"
huawei_sms_app_key: "your_app_key_here"
huawei_sms_app_secret: "your_app_secret_here"
huawei_sms_sender: "your_sender_here"
huawei_sms_template_id: "your_template_id_here"
huawei_sms_signature: "your_signature_here"
huawei_sms_concurrent_limit: 10

micro_realname_service: "com.zhigui.xian.realname.aliyun"

# 在现有配置中添加以下内容
wechat:
  templates:
    audit_apply: "8LW5RWD2m5eo4tNfI3aeYRQ2Y9F5bAdGL546efctmI0"  # 审批申请提醒
    audit_result: "ODMiluCapUjiDwg7T5CvbOg6BxmVUPbIDGjJj3xcjVE" # 认证结果通知